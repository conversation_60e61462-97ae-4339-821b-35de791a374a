(()=>{var e={};e.id=974,e.ids=[974],e.modules={301:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=r(5239),o=r(8088),n=r(8170),i=r.n(n),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(s,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,597)),"C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},597:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(7413);function o(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-center mb-8",children:"Sistema Contable"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Gesti\xf3n de Inventarios"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Sistema completo para la gesti\xf3n de inventarios con m\xe9todos PEPS, UEPS y Promedio Ponderado."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCE6 Productos"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Gestiona tu cat\xe1logo de productos"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCCA Inventario"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"M\xe9todos de valoraci\xf3n de inventario"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCCB Movimientos"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Registra compras, ventas y devoluciones"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"\uD83D\uDCC8 Reportes"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Tarjeta de almac\xe9n y reportes"})]})]})]})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1357:()=>{},2704:()=>{},2741:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6309:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6487:()=>{},7741:()=>{},8014:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n,metadata:()=>o});var t=r(7413);r(2704);let o={title:"Sistema Contable",description:"Sistema de inventarios"};function n({children:e}){return(0,t.jsx)("html",{lang:"es",children:(0,t.jsx)("body",{children:e})})}},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[825],()=>r(301));module.exports=t})();