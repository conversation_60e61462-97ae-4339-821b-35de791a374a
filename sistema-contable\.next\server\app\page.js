(()=>{var e={};e.id=974,e.ids=[974],e.modules={301:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>l});var r=t(5239),o=t(8088),n=t(8170),a=t.n(n),i=t(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,597)),"C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Downloads\\Cursos bb\\Proyecto conta\\sistema-contable\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(7413);function o(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Sistema Contable"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Gesti\xf3n de inventarios con m\xe9todos PEPS, UEPS y Promedio Ponderado"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Funcionalidades Principales"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"\uD83D\uDCE6 Gesti\xf3n de Productos"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Registra y administra tu cat\xe1logo de productos"})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"\uD83D\uDCCA M\xe9todos de Inventario"}),(0,r.jsx)("p",{className:"text-gray-600",children:"PEPS, UEPS y Promedio Ponderado"})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"\uD83D\uDCCB Tarjeta de Almac\xe9n"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Historial completo de movimientos"})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"\uD83D\uDD04 Operaciones"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Compras, ventas y devoluciones"})]})]}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Desarrollado con Next.js, React, Tailwind CSS, shadcn/ui, Framer Motion y Firebase"})})]})]})})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1357:()=>{},2704:()=>{},2741:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6309:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},6487:()=>{},7741:()=>{},8014:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>o});var r=t(7413);t(2704);let o={title:"Sistema Contable - Gesti\xf3n de Inventarios",description:"Sistema de contabilidad para gesti\xf3n de inventarios con m\xe9todos PEPS, UEPS y Promedio Ponderado"};function n({children:e}){return(0,r.jsx)("html",{lang:"es",children:(0,r.jsx)("body",{className:"bg-gray-50",children:e})})}},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[825],()=>t(301));module.exports=r})();