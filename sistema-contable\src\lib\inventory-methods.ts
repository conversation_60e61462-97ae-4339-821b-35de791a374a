import { 
  InventoryMovement, 
  WarehouseCardEntry, 
  InventoryMethod, 
  InventoryCalculationResult,
  OperationType 
} from '@/types/inventory';

// Interfaz para lotes de inventario (usado en PEPS y UEPS)
interface InventoryBatch {
  quantity: number;
  unitPrice: number;
  date: Date;
}

/**
 * Calcula el inventario usando el método PEPS (Primeras Entradas, Primeras Salidas)
 */
export function calculatePEPS(movements: InventoryMovement[]): InventoryCalculationResult {
  const sortedMovements = [...movements].sort((a, b) => a.date.getTime() - b.date.getTime());
  const warehouseEntries: WarehouseCardEntry[] = [];
  const batches: InventoryBatch[] = [];
  let currentStock = 0;
  let totalValue = 0;

  for (const movement of sortedMovements) {
    let remainingQuantity = movement.quantity;
    let operationCost = 0;

    if (movement.operationType === 'COMPRA' || movement.operationType === 'DEVOLUCION_VENTA') {
      // Entrada de inventario
      batches.push({
        quantity: movement.quantity,
        unitPrice: movement.unitPrice,
        date: movement.date
      });
      currentStock += movement.quantity;
      operationCost = movement.quantity * movement.unitPrice;
      totalValue += operationCost;
    } else {
      // Salida de inventario (VENTA o DEVOLUCION_COMPRA)
      while (remainingQuantity > 0 && batches.length > 0) {
        const oldestBatch = batches[0];
        const quantityToTake = Math.min(remainingQuantity, oldestBatch.quantity);
        
        operationCost += quantityToTake * oldestBatch.unitPrice;
        oldestBatch.quantity -= quantityToTake;
        remainingQuantity -= quantityToTake;
        
        if (oldestBatch.quantity === 0) {
          batches.shift();
        }
      }
      
      currentStock -= movement.quantity;
      totalValue -= operationCost;
    }

    const averageUnitCost = currentStock > 0 ? totalValue / currentStock : 0;

    warehouseEntries.push({
      id: movement.id,
      productId: movement.productId,
      date: movement.date,
      operationType: movement.operationType,
      quantity: movement.quantity,
      unitPrice: movement.unitPrice,
      total: movement.total,
      currentStock,
      accumulatedCost: totalValue,
      averageUnitCost,
      description: movement.description
    });
  }

  return {
    currentStock,
    totalValue,
    averageUnitCost: currentStock > 0 ? totalValue / currentStock : 0,
    movements: warehouseEntries
  };
}

/**
 * Calcula el inventario usando el método UEPS (Últimas Entradas, Primeras Salidas)
 */
export function calculateUEPS(movements: InventoryMovement[]): InventoryCalculationResult {
  const sortedMovements = [...movements].sort((a, b) => a.date.getTime() - b.date.getTime());
  const warehouseEntries: WarehouseCardEntry[] = [];
  const batches: InventoryBatch[] = [];
  let currentStock = 0;
  let totalValue = 0;

  for (const movement of sortedMovements) {
    let remainingQuantity = movement.quantity;
    let operationCost = 0;

    if (movement.operationType === 'COMPRA' || movement.operationType === 'DEVOLUCION_VENTA') {
      // Entrada de inventario
      batches.push({
        quantity: movement.quantity,
        unitPrice: movement.unitPrice,
        date: movement.date
      });
      currentStock += movement.quantity;
      operationCost = movement.quantity * movement.unitPrice;
      totalValue += operationCost;
    } else {
      // Salida de inventario (VENTA o DEVOLUCION_COMPRA)
      // En UEPS tomamos desde el final del array (últimas entradas)
      while (remainingQuantity > 0 && batches.length > 0) {
        const newestBatch = batches[batches.length - 1];
        const quantityToTake = Math.min(remainingQuantity, newestBatch.quantity);
        
        operationCost += quantityToTake * newestBatch.unitPrice;
        newestBatch.quantity -= quantityToTake;
        remainingQuantity -= quantityToTake;
        
        if (newestBatch.quantity === 0) {
          batches.pop();
        }
      }
      
      currentStock -= movement.quantity;
      totalValue -= operationCost;
    }

    const averageUnitCost = currentStock > 0 ? totalValue / currentStock : 0;

    warehouseEntries.push({
      id: movement.id,
      productId: movement.productId,
      date: movement.date,
      operationType: movement.operationType,
      quantity: movement.quantity,
      unitPrice: movement.unitPrice,
      total: movement.total,
      currentStock,
      accumulatedCost: totalValue,
      averageUnitCost,
      description: movement.description
    });
  }

  return {
    currentStock,
    totalValue,
    averageUnitCost: currentStock > 0 ? totalValue / currentStock : 0,
    movements: warehouseEntries
  };
}

/**
 * Calcula el inventario usando el método de Promedio Ponderado
 */
export function calculatePromedioPonderado(movements: InventoryMovement[]): InventoryCalculationResult {
  const sortedMovements = [...movements].sort((a, b) => a.date.getTime() - b.date.getTime());
  const warehouseEntries: WarehouseCardEntry[] = [];
  let currentStock = 0;
  let totalValue = 0;
  let averageUnitCost = 0;

  for (const movement of sortedMovements) {
    if (movement.operationType === 'COMPRA' || movement.operationType === 'DEVOLUCION_VENTA') {
      // Entrada de inventario
      const newValue = movement.quantity * movement.unitPrice;
      totalValue += newValue;
      currentStock += movement.quantity;
      
      // Recalcular promedio ponderado
      averageUnitCost = currentStock > 0 ? totalValue / currentStock : 0;
    } else {
      // Salida de inventario (VENTA o DEVOLUCION_COMPRA)
      const operationCost = movement.quantity * averageUnitCost;
      totalValue -= operationCost;
      currentStock -= movement.quantity;
      
      // El promedio ponderado se mantiene igual después de una salida
      if (currentStock <= 0) {
        averageUnitCost = 0;
        totalValue = 0;
      }
    }

    warehouseEntries.push({
      id: movement.id,
      productId: movement.productId,
      date: movement.date,
      operationType: movement.operationType,
      quantity: movement.quantity,
      unitPrice: movement.unitPrice,
      total: movement.total,
      currentStock,
      accumulatedCost: totalValue,
      averageUnitCost,
      description: movement.description
    });
  }

  return {
    currentStock,
    totalValue,
    averageUnitCost,
    movements: warehouseEntries
  };
}

/**
 * Función principal para calcular inventario según el método seleccionado
 */
export function calculateInventory(
  movements: InventoryMovement[], 
  method: InventoryMethod
): InventoryCalculationResult {
  switch (method) {
    case 'PEPS':
      return calculatePEPS(movements);
    case 'UEPS':
      return calculateUEPS(movements);
    case 'PROMEDIO_PONDERADO':
      return calculatePromedioPonderado(movements);
    default:
      throw new Error(`Método de inventario no soportado: ${method}`);
  }
}
