{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e862361d9b9d16873e21d487cbad4c6b", "previewModeSigningKey": "84aef287ad6685e7b8ffa46848048d4799d95ef06ad32049c15feb319e329bc7", "previewModeEncryptionKey": "7bbfed4567168e58d644ae9be9b33e116f16267ba11b90b993634214d00d0081"}}