{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "2f78bcfe23212f01cb75dc6cbb7337b2", "previewModeSigningKey": "becd19c71ab18ae590b83a1e28b7776cec001ba881ce21d57dd02eb7196e02b5", "previewModeEncryptionKey": "96350cc0cf0246bcafb81758685c5425230c82c8c93c6aa8bbdc6e22af340e5c"}}