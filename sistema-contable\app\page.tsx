export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Sistema Contable
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Gestión de inventarios con métodos PEPS, UEPS y Promedio Ponderado
          </p>

          <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">
              Funcionalidades Principales
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-left">
                <h3 className="font-semibold text-gray-700 mb-2">📦 Gestión de Productos</h3>
                <p className="text-gray-600">Registra y administra tu catálogo de productos</p>
              </div>

              <div className="text-left">
                <h3 className="font-semibold text-gray-700 mb-2">📊 Métodos de Inventario</h3>
                <p className="text-gray-600">PEPS, UEPS y Promedio Ponderado</p>
              </div>

              <div className="text-left">
                <h3 className="font-semibold text-gray-700 mb-2">📋 Tarjeta de Almacén</h3>
                <p className="text-gray-600">Historial completo de movimientos</p>
              </div>

              <div className="text-left">
                <h3 className="font-semibold text-gray-700 mb-2">🔄 Operaciones</h3>
                <p className="text-gray-600">Compras, ventas y devoluciones</p>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-sm text-gray-500">
                Desarrollado con Next.js, React, Tailwind CSS, shadcn/ui, Framer Motion y Firebase
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
