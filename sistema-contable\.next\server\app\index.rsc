1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[9665,[],"MetadataBoundary"]
6:I[9665,[],"OutletBoundary"]
9:I[4911,[],"AsyncMetadataOutlet"]
b:I[9665,[],"ViewportBoundary"]
d:I[6614,[],""]
:HL["/_next/static/css/3841d4c5a07bdedb.css","style"]
0:{"P":null,"b":"eNAWWuyu5K2uvKgleKqwW","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/3841d4c5a07bdedb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"es","children":["$","body",null,{"children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50 p-8","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":[["$","h1",null,{"className":"text-4xl font-bold text-center mb-8","children":"Sistema Contable"}],["$","div",null,{"className":"bg-white rounded-lg shadow-lg p-8","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Gestión de Inventarios"}],["$","p",null,{"className":"text-gray-600 mb-6","children":"Sistema completo para la gestión de inventarios con métodos PEPS, UEPS y Promedio Ponderado."}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"className":"p-4 border rounded-lg","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"📦 Productos"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Gestiona tu catálogo de productos"}]]}],["$","div",null,{"className":"p-4 border rounded-lg","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"📊 Inventario"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Métodos de valoración de inventario"}]]}],["$","div",null,{"className":"p-4 border rounded-lg","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"📋 Movimientos"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Registra compras, ventas y devoluciones"}]]}],["$","div",null,{"className":"p-4 border rounded-lg","children":[["$","h3",null,{"className":"font-semibold mb-2","children":"📈 Reportes"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Tarjeta de almacén y reportes"}]]}]]}]]}]]}]}],["$","$L4",null,{"children":"$L5"}],null,["$","$L6",null,{"children":["$L7","$L8",["$","$L9",null,{"promise":"$@a"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","6COakvDl5NW9nlN_YgAVY",{"children":[["$","$Lb",null,{"children":"$Lc"}],null]}],null]}],false]],"m":"$undefined","G":["$d","$undefined"],"s":false,"S":true}
e:"$Sreact.suspense"
f:I[4911,[],"AsyncMetadata"]
5:["$","$e",null,{"fallback":null,"children":["$","$Lf",null,{"promise":"$@10"}]}]
8:null
c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
7:null
10:{"metadata":[["$","title","0",{"children":"Sistema Contable"}],["$","meta","1",{"name":"description","content":"Sistema de inventarios"}]],"error":null,"digest":"$undefined"}
a:{"metadata":"$10:metadata","error":null,"digest":"$undefined"}
