1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[9665,[],"MetadataBoundary"]
6:I[9665,[],"OutletBoundary"]
9:I[4911,[],"AsyncMetadataOutlet"]
b:I[9665,[],"ViewportBoundary"]
d:I[6614,[],""]
:HL["/_next/static/css/92f2908b31c16bde.css","style"]
0:{"P":null,"b":"eykJhlql7ei6CEsE13hTf","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/92f2908b31c16bde.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"es","children":["$","body",null,{"className":"bg-gray-50","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50 py-12","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-4xl font-bold text-gray-900 mb-4","children":"Sistema Contable"}],["$","p",null,{"className":"text-xl text-gray-600 mb-8","children":"Gestión de inventarios con métodos PEPS, UEPS y Promedio Ponderado"}],["$","div",null,{"className":"bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-6","children":"Funcionalidades Principales"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-6","children":[["$","div",null,{"className":"text-left","children":[["$","h3",null,{"className":"font-semibold text-gray-700 mb-2","children":"📦 Gestión de Productos"}],["$","p",null,{"className":"text-gray-600","children":"Registra y administra tu catálogo de productos"}]]}],["$","div",null,{"className":"text-left","children":[["$","h3",null,{"className":"font-semibold text-gray-700 mb-2","children":"📊 Métodos de Inventario"}],["$","p",null,{"className":"text-gray-600","children":"PEPS, UEPS y Promedio Ponderado"}]]}],["$","div",null,{"className":"text-left","children":[["$","h3",null,{"className":"font-semibold text-gray-700 mb-2","children":"📋 Tarjeta de Almacén"}],["$","p",null,{"className":"text-gray-600","children":"Historial completo de movimientos"}]]}],["$","div",null,{"className":"text-left","children":[["$","h3",null,{"className":"font-semibold text-gray-700 mb-2","children":"🔄 Operaciones"}],["$","p",null,{"className":"text-gray-600","children":"Compras, ventas y devoluciones"}]]}]]}],["$","div",null,{"className":"mt-8","children":["$","p",null,{"className":"text-sm text-gray-500","children":"Desarrollado con Next.js, React, Tailwind CSS, shadcn/ui, Framer Motion y Firebase"}]}]]}]]}]}]}],["$","$L4",null,{"children":"$L5"}],null,["$","$L6",null,{"children":["$L7","$L8",["$","$L9",null,{"promise":"$@a"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","NRA1MDQL-HKzB8bD2OPzu",{"children":[["$","$Lb",null,{"children":"$Lc"}],null]}],null]}],false]],"m":"$undefined","G":["$d","$undefined"],"s":false,"S":true}
e:"$Sreact.suspense"
f:I[4911,[],"AsyncMetadata"]
5:["$","$e",null,{"fallback":null,"children":["$","$Lf",null,{"promise":"$@10"}]}]
8:null
c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
7:null
10:{"metadata":[["$","title","0",{"children":"Sistema Contable - Gestión de Inventarios"}],["$","meta","1",{"name":"description","content":"Sistema de contabilidad para gestión de inventarios con métodos PEPS, UEPS y Promedio Ponderado"}]],"error":null,"digest":"$undefined"}
a:{"metadata":"$10:metadata","error":null,"digest":"$undefined"}
