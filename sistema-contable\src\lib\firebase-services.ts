import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import {
  Product,
  InventoryMovement,
  ProductFormData,
  MovementFormData,
  ProductFirestore,
  InventoryMovementFirestore,
  ProductFilter,
  MovementFilter
} from '@/types/inventory';

// Collections
const PRODUCTS_COLLECTION = 'products';
const MOVEMENTS_COLLECTION = 'inventory_movements';

// Helper functions to convert between Firestore and app types
function firestoreToProduct(id: string, data: ProductFirestore): Product {
  return {
    id,
    name: data.name,
    description: data.description,
    category: data.category,
    unit: data.unit,
    createdAt: data.createdAt.toDate(),
    updatedAt: data.updatedAt.toDate()
  };
}

function productToFirestore(product: ProductFormData): ProductFirestore {
  const now = Timestamp.now();
  return {
    name: product.name,
    description: product.description,
    category: product.category,
    unit: product.unit,
    createdAt: now,
    updatedAt: now
  };
}

function firestoreToMovement(id: string, data: InventoryMovementFirestore): InventoryMovement {
  return {
    id,
    productId: data.productId,
    date: data.date.toDate(),
    operationType: data.operationType,
    quantity: data.quantity,
    unitPrice: data.unitPrice,
    total: data.total,
    description: data.description,
    createdAt: data.createdAt.toDate()
  };
}

function movementToFirestore(movement: MovementFormData): InventoryMovementFirestore {
  const now = Timestamp.now();
  return {
    productId: movement.productId,
    date: now,
    operationType: movement.operationType,
    quantity: movement.quantity,
    unitPrice: movement.unitPrice,
    total: movement.quantity * movement.unitPrice,
    description: movement.description,
    createdAt: now
  };
}

// Product services
export const productService = {
  // Create a new product
  async create(productData: ProductFormData): Promise<string> {
    try {
      const docRef = await addDoc(
        collection(db, PRODUCTS_COLLECTION),
        productToFirestore(productData)
      );
      return docRef.id;
    } catch (error) {
      console.error('Error creating product:', error);
      throw new Error('Failed to create product');
    }
  },

  // Get all products with optional filtering
  async getAll(filter?: ProductFilter): Promise<Product[]> {
    try {
      let q = query(collection(db, PRODUCTS_COLLECTION));

      // Apply filters
      if (filter?.category) {
        q = query(q, where('category', '==', filter.category));
      }

      // Apply sorting
      if (filter?.sortBy) {
        const direction = filter.sortOrder === 'desc' ? 'desc' : 'asc';
        q = query(q, orderBy(filter.sortBy, direction));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }

      const querySnapshot = await getDocs(q);
      let products = querySnapshot.docs.map(doc => 
        firestoreToProduct(doc.id, doc.data() as ProductFirestore)
      );

      // Apply search filter (client-side for simplicity)
      if (filter?.search) {
        const searchTerm = filter.search.toLowerCase();
        products = products.filter(product => 
          product.name.toLowerCase().includes(searchTerm) ||
          (product.description && product.description.toLowerCase().includes(searchTerm))
        );
      }

      return products;
    } catch (error) {
      console.error('Error getting products:', error);
      throw new Error('Failed to get products');
    }
  },

  // Get a single product by ID
  async getById(id: string): Promise<Product | null> {
    try {
      const docRef = doc(db, PRODUCTS_COLLECTION, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return firestoreToProduct(docSnap.id, docSnap.data() as ProductFirestore);
      }
      return null;
    } catch (error) {
      console.error('Error getting product:', error);
      throw new Error('Failed to get product');
    }
  },

  // Update a product
  async update(id: string, productData: Partial<ProductFormData>): Promise<void> {
    try {
      const docRef = doc(db, PRODUCTS_COLLECTION, id);
      const updateData: Partial<ProductFirestore> = {
        ...productData,
        updatedAt: Timestamp.now()
      };
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  },

  // Delete a product
  async delete(id: string): Promise<void> {
    try {
      // Check if product has movements
      const movementsQuery = query(
        collection(db, MOVEMENTS_COLLECTION),
        where('productId', '==', id),
        limit(1)
      );
      const movementsSnapshot = await getDocs(movementsQuery);
      
      if (!movementsSnapshot.empty) {
        throw new Error('Cannot delete product with existing movements');
      }

      const docRef = doc(db, PRODUCTS_COLLECTION, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }
};

// Movement services
export const movementService = {
  // Create a new movement
  async create(movementData: MovementFormData): Promise<string> {
    try {
      const docRef = await addDoc(
        collection(db, MOVEMENTS_COLLECTION),
        movementToFirestore(movementData)
      );
      return docRef.id;
    } catch (error) {
      console.error('Error creating movement:', error);
      throw new Error('Failed to create movement');
    }
  },

  // Get all movements with optional filtering
  async getAll(filter?: MovementFilter): Promise<InventoryMovement[]> {
    try {
      let q = query(collection(db, MOVEMENTS_COLLECTION));

      // Apply filters
      if (filter?.productId) {
        q = query(q, where('productId', '==', filter.productId));
      }

      if (filter?.operationType) {
        q = query(q, where('operationType', '==', filter.operationType));
      }

      // Apply sorting
      if (filter?.sortBy) {
        const direction = filter.sortOrder === 'desc' ? 'desc' : 'asc';
        q = query(q, orderBy(filter.sortBy, direction));
      } else {
        q = query(q, orderBy('date', 'asc'));
      }

      const querySnapshot = await getDocs(q);
      let movements = querySnapshot.docs.map(doc => 
        firestoreToMovement(doc.id, doc.data() as InventoryMovementFirestore)
      );

      // Apply date filters (client-side)
      if (filter?.dateFrom || filter?.dateTo) {
        movements = movements.filter(movement => {
          const movementDate = movement.date;
          if (filter.dateFrom && movementDate < filter.dateFrom) return false;
          if (filter.dateTo && movementDate > filter.dateTo) return false;
          return true;
        });
      }

      return movements;
    } catch (error) {
      console.error('Error getting movements:', error);
      throw new Error('Failed to get movements');
    }
  },

  // Get movements for a specific product
  async getByProductId(productId: string): Promise<InventoryMovement[]> {
    return this.getAll({ productId, sortBy: 'date', sortOrder: 'asc' });
  },

  // Delete a movement
  async delete(id: string): Promise<void> {
    try {
      const docRef = doc(db, MOVEMENTS_COLLECTION, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting movement:', error);
      throw new Error('Failed to delete movement');
    }
  }
};
