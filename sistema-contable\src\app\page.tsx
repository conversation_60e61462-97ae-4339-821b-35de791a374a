'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Package,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ProductForm from '@/components/products/ProductForm';
import MovementForm from '@/components/movements/MovementForm';
import { productService, movementService } from '@/lib/firebase-services';
import { formatCurrency } from '@/lib/utils';
import { Product, InventoryMovement } from '@/types/inventory';

interface DashboardStats {
  totalProducts: number;
  totalMovements: number;
  recentMovements: InventoryMovement[];
  totalValue: number;
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalMovements: 0,
    recentMovements: [],
    totalValue: 0
  });
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load products and movements in parallel
      const [products, movements] = await Promise.all([
        productService.getAll(),
        movementService.getAll({ sortBy: 'date', sortOrder: 'desc' })
      ]);

      // Calculate total value (simplified - in real app you'd use inventory methods)
      const totalValue = movements.reduce((acc, movement) => {
        if (movement.operationType === 'COMPRA' || movement.operationType === 'DEVOLUCION_VENTA') {
          return acc + movement.total;
        } else {
          return acc - movement.total;
        }
      }, 0);

      setStats({
        totalProducts: products.length,
        totalMovements: movements.length,
        recentMovements: movements.slice(0, 5),
        totalValue: Math.max(0, totalValue)
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const getOperationIcon = (operationType: string) => {
    switch (operationType) {
      case 'COMPRA':
      case 'DEVOLUCION_VENTA':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'VENTA':
      case 'DEVOLUCION_COMPRA':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getOperationLabel = (operationType: string) => {
    const labels: Record<string, string> = {
      'COMPRA': 'Compra',
      'VENTA': 'Venta',
      'DEVOLUCION_VENTA': 'Dev. Venta',
      'DEVOLUCION_COMPRA': 'Dev. Compra'
    };
    return labels[operationType] || operationType;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Resumen general del sistema de inventarios
            </p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <ProductForm onProductCreated={handleRefresh} />
            <MovementForm onMovementCreated={handleRefresh} />
          </div>
        </div>
      </motion.div>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Productos
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProducts}</div>
              <p className="text-xs text-muted-foreground">
                Productos registrados
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Movimientos
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMovements}</div>
              <p className="text-xs text-muted-foreground">
                Movimientos registrados
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Valor Total
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Valor estimado del inventario
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Actividad Reciente
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recentMovements.length}</div>
              <p className="text-xs text-muted-foreground">
                Movimientos recientes
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Recent Movements */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Movimientos Recientes</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.recentMovements.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay movimientos
                </h3>
                <p className="text-gray-500 mb-4">
                  Comienza registrando tu primer movimiento de inventario.
                </p>
                <MovementForm
                  onMovementCreated={handleRefresh}
                  trigger={
                    <Button>
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Registrar Movimiento
                    </Button>
                  }
                />
              </div>
            ) : (
              <div className="space-y-4">
                {stats.recentMovements.map((movement, index) => (
                  <motion.div
                    key={movement.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      {getOperationIcon(movement.operationType)}
                      <div>
                        <p className="font-medium text-gray-900">
                          {getOperationLabel(movement.operationType)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Cantidad: {movement.quantity} | {formatCurrency(movement.unitPrice)} c/u
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(movement.total)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {movement.date.toLocaleDateString('es-CO')}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
