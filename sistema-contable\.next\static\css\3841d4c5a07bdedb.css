/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,::backdrop,:after,:before{--tw-border-style:solid}}}.mx-auto{margin-inline:auto}.grid{display:grid}.min-h-screen{min-height:100vh}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.border{border-style:var(--tw-border-style);border-width:1px}.text-center{text-align:center}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}