'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Package } from 'lucide-react';
import ProductForm from '@/components/products/ProductForm';
import ProductList from '@/components/products/ProductList';

export default function ProductsPage() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleProductCreated = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-3">
              <Package className="h-8 w-8 text-blue-600" />
              <span>Gestión de Productos</span>
            </h1>
            <p className="text-gray-600 mt-1">
              Administra el catálogo de productos de tu inventario
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <ProductForm onProductCreated={handleProductCreated} />
          </div>
        </div>
      </motion.div>

      {/* Product List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <ProductList refreshTrigger={refreshTrigger} />
      </motion.div>
    </div>
  );
}
