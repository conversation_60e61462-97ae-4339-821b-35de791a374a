// Tipos para el sistema de inventario contable

export type InventoryMethod = 'PEPS' | 'UEPS' | 'PROMEDIO_PONDERADO';

export type OperationType = 'COMPRA' | 'VENTA' | 'DEVOLUCION_VENTA' | 'DEVOLUCION_COMPRA';

export interface Product {
  id: string;
  name: string;
  description?: string;
  category?: string;
  unit: string; // unidad de medida (kg, unidades, litros, etc.)
  createdAt: Date;
  updatedAt: Date;
}

export interface InventoryMovement {
  id: string;
  productId: string;
  date: Date;
  operationType: OperationType;
  quantity: number;
  unitPrice: number;
  total: number;
  description?: string;
  createdAt: Date;
}

export interface WarehouseCardEntry {
  id: string;
  productId: string;
  date: Date;
  operationType: OperationType;
  quantity: number;
  unitPrice: number;
  total: number;
  currentStock: number;
  accumulatedCost: number;
  averageUnitCost?: number; // Para método promedio ponderado
  description?: string;
}

export interface ProductStock {
  productId: string;
  currentStock: number;
  totalValue: number;
  averageUnitCost: number;
  lastMovementDate: Date;
}

export interface InventoryCalculationResult {
  currentStock: number;
  totalValue: number;
  averageUnitCost: number;
  movements: WarehouseCardEntry[];
}

// Interfaces para Firebase
export interface ProductFirestore extends Omit<Product, 'id' | 'createdAt' | 'updatedAt'> {
  createdAt: any; // Firestore Timestamp
  updatedAt: any; // Firestore Timestamp
}

export interface InventoryMovementFirestore extends Omit<InventoryMovement, 'id' | 'date' | 'createdAt'> {
  date: any; // Firestore Timestamp
  createdAt: any; // Firestore Timestamp
}

export interface WarehouseCardEntryFirestore extends Omit<WarehouseCardEntry, 'id' | 'date'> {
  date: any; // Firestore Timestamp
}

// Tipos para formularios
export interface ProductFormData {
  name: string;
  description?: string;
  category?: string;
  unit: string;
}

export interface MovementFormData {
  productId: string;
  operationType: OperationType;
  quantity: number;
  unitPrice: number;
  description?: string;
}

// Tipos para filtros y búsquedas
export interface ProductFilter {
  search?: string;
  category?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface MovementFilter {
  productId?: string;
  operationType?: OperationType;
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'date' | 'operationType' | 'quantity';
  sortOrder?: 'asc' | 'desc';
}
