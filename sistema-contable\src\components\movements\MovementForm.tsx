'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Loader2, TrendingUp, TrendingDown } from 'lucide-react';
import { MovementFormData, OperationType, Product } from '@/types/inventory';
import { movementService, productService } from '@/lib/firebase-services';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface MovementFormProps {
  onMovementCreated?: () => void;
  trigger?: React.ReactNode;
  preselectedProductId?: string;
}

const OPERATION_TYPES: { value: OperationType; label: string; icon: React.ReactNode; color: string }[] = [
  { 
    value: 'COMPRA', 
    label: 'Compra', 
    icon: <TrendingUp className="h-4 w-4" />, 
    color: 'text-green-600' 
  },
  { 
    value: 'VENTA', 
    label: 'Venta', 
    icon: <TrendingDown className="h-4 w-4" />, 
    color: 'text-red-600' 
  },
  { 
    value: 'DEVOLUCION_VENTA', 
    label: 'Devolución sobre Venta', 
    icon: <TrendingUp className="h-4 w-4" />, 
    color: 'text-blue-600' 
  },
  { 
    value: 'DEVOLUCION_COMPRA', 
    label: 'Devolución sobre Compra', 
    icon: <TrendingDown className="h-4 w-4" />, 
    color: 'text-orange-600' 
  },
];

export default function MovementForm({ 
  onMovementCreated, 
  trigger, 
  preselectedProductId 
}: MovementFormProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [formData, setFormData] = useState<MovementFormData>({
    productId: preselectedProductId || '',
    operationType: 'COMPRA',
    quantity: 0,
    unitPrice: 0,
    description: '',
  });

  const loadProducts = async () => {
    try {
      const fetchedProducts = await productService.getAll();
      setProducts(fetchedProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('Error al cargar los productos');
    }
  };

  useEffect(() => {
    if (open) {
      loadProducts();
    }
  }, [open]);

  useEffect(() => {
    if (preselectedProductId) {
      setFormData(prev => ({
        ...prev,
        productId: preselectedProductId
      }));
    }
  }, [preselectedProductId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.productId || !formData.operationType || formData.quantity <= 0 || formData.unitPrice <= 0) {
      toast.error('Por favor completa todos los campos obligatorios');
      return;
    }

    setLoading(true);
    
    try {
      await movementService.create(formData);
      toast.success('Movimiento registrado exitosamente');
      
      // Reset form
      setFormData({
        productId: preselectedProductId || '',
        operationType: 'COMPRA',
        quantity: 0,
        unitPrice: 0,
        description: '',
      });
      
      setOpen(false);
      onMovementCreated?.();
    } catch (error) {
      console.error('Error creating movement:', error);
      toast.error('Error al registrar el movimiento');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof MovementFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const selectedProduct = products.find(p => p.id === formData.productId);
  const total = formData.quantity * formData.unitPrice;

  const defaultTrigger = (
    <Button className="flex items-center space-x-2">
      <Plus className="h-4 w-4" />
      <span>Nuevo Movimiento</span>
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Registrar Movimiento de Inventario</DialogTitle>
        </DialogHeader>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent className="pt-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Producto */}
                <div className="space-y-2">
                  <Label htmlFor="productId">
                    Producto <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.productId}
                    onValueChange={(value) => handleInputChange('productId', value)}
                    required
                    disabled={!!preselectedProductId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un producto" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          <div className="flex flex-col">
                            <span>{product.name}</span>
                            <span className="text-sm text-gray-500">
                              Unidad: {product.unit}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedProduct && (
                    <p className="text-sm text-gray-600">
                      Unidad de medida: {selectedProduct.unit}
                    </p>
                  )}
                </div>

                {/* Tipo de operación */}
                <div className="space-y-2">
                  <Label htmlFor="operationType">
                    Tipo de Operación <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.operationType}
                    onValueChange={(value: OperationType) => handleInputChange('operationType', value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona el tipo de operación" />
                    </SelectTrigger>
                    <SelectContent>
                      {OPERATION_TYPES.map((operation) => (
                        <SelectItem key={operation.value} value={operation.value}>
                          <div className={`flex items-center space-x-2 ${operation.color}`}>
                            {operation.icon}
                            <span>{operation.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Cantidad */}
                  <div className="space-y-2">
                    <Label htmlFor="quantity">
                      Cantidad <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="0.01"
                      step="0.01"
                      placeholder="0"
                      value={formData.quantity || ''}
                      onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>

                  {/* Precio unitario */}
                  <div className="space-y-2">
                    <Label htmlFor="unitPrice">
                      Precio Unitario <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="unitPrice"
                      type="number"
                      min="0.01"
                      step="0.01"
                      placeholder="0"
                      value={formData.unitPrice || ''}
                      onChange={(e) => handleInputChange('unitPrice', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>
                </div>

                {/* Total calculado */}
                {total > 0 && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Total:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {formatCurrency(total)}
                      </span>
                    </div>
                  </div>
                )}

                {/* Descripción */}
                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Input
                    id="description"
                    type="text"
                    placeholder="Descripción opcional del movimiento"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                  />
                </div>

                {/* Botones */}
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setOpen(false)}
                    disabled={loading}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Registrando...
                      </>
                    ) : (
                      'Registrar Movimiento'
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
